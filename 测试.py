import json
import sys
import re
import os

# 设置环境变量避免TensorFlow冲突
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
os.environ['CUDA_VISIBLE_DEVICES'] = ''

try:
    from transformers import AutoModelForCausalLM, AutoTokenizer
    import torch
    MODEL_AVAILABLE = True
    print("模型库加载成功")
except ImportError as e:
    print(f"模型库加载失败: {e}")
    print("将使用简化模式运行")
    MODEL_AVAILABLE = False

# 路径配置
model_path = "Qwen/Qwen2.5-3B-Instruct"  # 使用更稳定的模型
data_files = {
    "船舶数据1": "57d55cae476818831e41b8a7edbe3007.json",
    "船舶数据2": "dbe6a7821cce479ab6152a446c88b3c4.json"
}

# 全局设置
MAX_NEW_TOKENS = 2048

class ShippingRAGSystem:
    def __init__(self, model_path=None):
        print("正在初始化港航数据RAG系统...")

        # 加载船舶数据并建立索引
        self.shipping_database = self._load_and_index_data()
        print(f"数据库建立完成，共{len(self.shipping_database)}条船舶记录")

        # RAG提示词模板
        self.rag_template = """作为港航数据专家，请根据以下知识库内容回答问题：

上下文数据：
{context}

用户问题：{question}

请根据上下文中的船舶数据准确回答问题。回答要求：
1. 必须基于上下文内容，不要编造数据
2. 提取准确的gcdid、cmch、aissbh、tgsj信息
3. 如果找不到相关信息，请明确说明
4. 保持专业的港航数据分析语调"""

        # 尝试加载模型
        if MODEL_AVAILABLE and model_path:
            try:
                print("正在加载AI模型，请稍候...")
                self.tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
                self.model = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    torch_dtype=torch.float16,
                    device_map="auto",
                    trust_remote_code=True
                )
                print("AI模型加载完成！")
                self.model_loaded = True
            except Exception as e:
                print(f"模型加载失败: {e}")
                print("将使用规则匹配模式")
                self.model_loaded = False
        else:
            print("使用规则匹配模式")
            self.model_loaded = False
        
        # 简化的RAG提示词模板
        self.rag_template = """你是港航数据查询专家。请根据提供的船舶数据准确回答用户问题。

数据库记录：
{context}

用户问题：{question}

请直接从数据库记录中提取准确信息回答，不要编造任何数据。如果找不到相关信息，请明确说明。"""

    def _load_and_index_data(self):
        """加载船舶数据并建立索引"""
        database = []
        
        for source_name, file_name in data_files.items():
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    file_data = json.load(f)
                
                # 提取data字段中的船舶记录
                if isinstance(file_data, dict) and 'data' in file_data:
                    ship_records = file_data['data']
                else:
                    ship_records = file_data
                
                # 为每条记录添加来源信息并建立索引
                for record in ship_records:
                    if isinstance(record, dict):
                        # 添加来源文件信息
                        record['source_file'] = source_name
                        database.append(record)
                
                print(f"已加载{source_name}: {len(ship_records)}条记录")
                
            except Exception as e:
                print(f"加载{source_name}失败: {str(e)}")
        
        return database
    
    def search_ships(self, query):
        """根据查询搜索相关船舶记录"""
        query_lower = query.lower()
        matched_ships = []
        
        # 提取查询中的关键信息
        ship_name = None
        ais_number = None
        time_info = None
        
        # 使用正则表达式提取信息
        # 提取船舶名称（中文字符+数字）
        ship_name_match = re.search(r'([一-龟\u4e00-\u9fff]+[货客运油\d]+\d*)', query)
        if ship_name_match:
            ship_name = ship_name_match.group(1)
        
        # 提取AIS号（纯数字）
        ais_match = re.search(r'ais.*?(\d{6,})', query_lower)
        if ais_match:
            ais_number = ais_match.group(1)
        
        # 提取时间信息
        time_match = re.search(r'(\d{4}[-年]\d{1,2}[-月]\d{1,2})', query)
        if time_match:
            time_info = time_match.group(1)
        
        # 在数据库中搜索匹配记录
        for record in self.shipping_database:
            # 船舶名称匹配
            if ship_name and record.get('cmch'):
                if ship_name in record['cmch']:
                    matched_ships.append((record, f"船舶名称匹配: {ship_name}"))
                    continue
            
            # AIS号匹配
            if ais_number and record.get('aissbh'):
                if str(record['aissbh']) == ais_number:
                    matched_ships.append((record, f"AIS号匹配: {ais_number}"))
                    continue
            
            # 时间匹配
            if time_info and record.get('tgsj'):
                if time_info.replace('年', '-').replace('月', '-') in record['tgsj']:
                    matched_ships.append((record, f"时间匹配: {time_info}"))
                    continue
        
        # 如果没有精确匹配，返回一些示例数据
        if not matched_ships:
            for record in self.shipping_database[:3]:
                matched_ships.append((record, "示例数据"))
        
        return matched_ships[:5]  # 返回前5个匹配结果

    def answer_question(self, question):
        """使用RAG回答用户问题"""
        # 判断是否为船舶相关查询
        is_shipping_query = self._is_shipping_related_query(question)

        if is_shipping_query:
            # 搜索相关船舶记录
            matched_ships = self.search_ships(question)

            if not matched_ships:
                # 没找到船舶数据，但是船舶相关查询，让AI回答
                if self.model_loaded:
                    return self._generate_ai_response(question, "没有找到相关的船舶数据。")
                else:
                    return "抱歉，没有找到相关的船舶数据。"

            # 格式化上下文数据
            context_parts = []
            for record, reason in matched_ships:
                context_parts.append(f"""
船舶记录：
- 观测点卡口ID(gcdid): {record.get('gcdid', '无')}
- 船舶名称(cmch): {record.get('cmch', '无')}
- AIS识别号(aissbh): {record.get('aissbh', '无')}
- 通过时间(tgsj): {record.get('tgsj', '无')}
- 数据来源: {record.get('source_file', '未知')}
- 匹配原因: {reason}
""")

            context = "\n".join(context_parts)

            # 如果模型可用，使用AI生成回复
            if self.model_loaded:
                return self._generate_ai_response(question, context)
            else:
                # 使用规则匹配生成回复
                return self._generate_rule_based_response(question, matched_ships)
        else:
            # 非船舶相关查询，直接让AI回答
            if self.model_loaded:
                return self._generate_general_ai_response(question)
            else:
                return "我是港航数据查询专家，专门回答船舶和港口相关的问题。请询问船舶数据相关的内容。"

    def _is_shipping_related_query(self, question):
        """判断是否为船舶相关查询"""
        shipping_keywords = [
            '船', '舶', 'ais', 'gcdid', 'cmch', 'tgsj', '港', '航',
            '货', '客', '运', '油', '江淮', '苏', '浙', '时间', '通过',
            '识别号', '观测点', '卡口'
        ]

        question_lower = question.lower()
        for keyword in shipping_keywords:
            if keyword in question_lower:
                return True

        # 检查是否包含船舶名称模式
        ship_name_pattern = r'([一-龟\u4e00-\u9fff]+[货客运油\d]+\d*)'
        if re.search(ship_name_pattern, question):
            return True

        return False

    def _generate_ai_response(self, question, context):
        """使用AI模型生成RAG回复"""
        # 构建RAG提示
        rag_prompt = self.rag_template.format(context=context, question=question)

        # 生成回答
        messages = [
            {"role": "user", "content": rag_prompt}
        ]

        return self._call_ai_model(messages)

    def _generate_general_ai_response(self, question):
        """使用AI模型生成一般回复"""
        messages = [
            {"role": "system", "content": "你是一个智能助手，可以回答各种问题。同时你也具备港航数据查询功能，当用户询问船舶相关信息时，你可以查询数据库提供准确信息。"},
            {"role": "user", "content": question}
        ]

        return self._call_ai_model(messages)

    def _call_ai_model(self, messages):
        """调用AI模型生成回复"""
        try:
            text = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )

            model_inputs = self.tokenizer([text], return_tensors="pt").to(self.model.device)

            with torch.no_grad():
                output_ids = self.model.generate(
                    **model_inputs,
                    max_new_tokens=MAX_NEW_TOKENS,
                    do_sample=True,
                    temperature=0.3,
                    top_p=0.8,
                    pad_token_id=self.tokenizer.eos_token_id
                )

            # 解码生成的文本
            response_ids = output_ids[0][len(model_inputs.input_ids[0]):]
            response = self.tokenizer.decode(response_ids, skip_special_tokens=True)
            return response.strip()

        except Exception as e:
            print(f"AI生成失败: {e}")
            return f"抱歉，AI模型暂时无法回答您的问题。错误信息：{str(e)}"

    def _generate_rule_based_response(self, question, matched_ships):
        """使用规则匹配生成回复"""
        response_parts = []

        for record, _ in matched_ships:
            # 根据问题类型生成准确回答
            if "aissbh" in question.lower() and "时间" in question:
                # 用户询问AIS号和时间
                ship_name = record.get('cmch', '未知')
                ais_number = record.get('aissbh', '无')
                time_info = record.get('tgsj', '无')
                response_parts.append(f"根据数据库查询，船舶{ship_name}的aissbh是{ais_number}，tgsj是{time_info}")

            elif "ais" in question.lower():
                # 用户询问AIS相关信息
                response_parts.append(f"AIS识别号{record.get('aissbh', '无')}对应的船舶信息：")
                response_parts.append(f"- 船舶名称: {record.get('cmch', '无')}")
                response_parts.append(f"- 观测点卡口ID: {record.get('gcdid', '无')}")
                response_parts.append(f"- 通过时间: {record.get('tgsj', '无')}")
                response_parts.append(f"- 数据来源: {record.get('source_file', '未知')}")

            else:
                # 通用信息显示
                response_parts.append(f"船舶{record.get('cmch', '未知')}的详细信息：")
                response_parts.append(f"- 观测点卡口ID(gcdid): {record.get('gcdid', '无')}")
                response_parts.append(f"- 船舶名称(cmch): {record.get('cmch', '无')}")
                response_parts.append(f"- AIS识别号(aissbh): {record.get('aissbh', '无')}")
                response_parts.append(f"- 通过时间(tgsj): {record.get('tgsj', '无')}")
                response_parts.append(f"- 数据来源: {record.get('source_file', '未知')}")

            response_parts.append("")  # 空行分隔

        return "\n".join(response_parts)
    
    def chat_with_user(self):
        """与用户进行港航数据查询对话"""
        print("\n=== 港航数据查询系统 ===")
        print("请输入您的查询问题，例如：")
        print("- 江淮货12的aissbh和时间是多少")
        print("- 查找AIS识别号为3957319的船舶信息")
        print("- 显示2024年9月9日的船舶数据")
        print("输入'quit'退出系统\n")
        
        while True:
            user_input = input("请输入查询: ")
            
            if user_input.lower() == 'quit':
                print("感谢使用港航数据查询系统！")
                break
            
            if not user_input.strip():
                print("请输入有效的查询内容")
                continue
            
            print("\n正在查询数据库...")
            try:
                response = self.answer_question(user_input)
                print(f"\n查询结果：\n{response}\n")
                print("-" * 50)
            except Exception as e:
                print(f"查询出错: {str(e)}")

    def test_queries(self):
        """测试一些示例查询"""
        test_questions = [
            "江淮货12的aissbh和时间是多少",
            "查找AIS识别号为3957319的船舶信息",
            "苏盐城货217898的详细信息",
            "2024年9月9日有哪些船舶通过"
        ]
        
        print("=== 测试查询 ===")
        for i, question in enumerate(test_questions, 1):
            print(f"\n测试 {i}: {question}")
            print("-" * 40)
            try:
                response = self.answer_question(question)
                print(f"回答: {response}")
            except Exception as e:
                print(f"错误: {str(e)}")
            print("=" * 60)


# 简化的函数定义
def initialize_system():
    """初始化港航数据查询系统"""
    system = ShippingRAGSystem(model_path)
    return system

def chat_with_model():
    """启动港航数据查询对话界面"""
    system = initialize_system()
    system.chat_with_user()

def test_system():
    """测试系统功能"""
    system = initialize_system()
    system.test_queries()

def simple_demo():
    """简单演示功能，不需要加载大模型"""
    print("=== 港航数据系统演示 ===\n")
    
    # 直接加载数据文件进行演示
    print("正在加载船舶数据...")
    all_data = []
    for source_name, file_name in data_files.items():
        try:
            with open(file_name, 'r', encoding='utf-8') as f:
                file_data = json.load(f)
            # 提取data字段中的船舶记录
            if isinstance(file_data, dict) and 'data' in file_data:
                ship_records = file_data['data']
            else:
                ship_records = file_data
            
            for record in ship_records:
                record['source_file'] = source_name
                all_data.append(record)
            
            print(f"已加载{source_name}: {len(ship_records)}条船舶记录")
        except Exception as e:
            print(f"加载{source_name}失败: {str(e)}")
    
    # 显示数据样例
    print(f"\n=== 数据库总计 {len(all_data)} 条记录 ===")
    
    # 查找江淮货12的示例
    print("\n示例查询：江淮货12的信息")
    for record in all_data:
        if record.get('cmch') and '江淮货12' in record['cmch']:
            print(f"找到匹配记录：")
            print(f"  观测点卡口ID(gcdid): {record.get('gcdid', '无')}")
            print(f"  船舶名称(cmch): {record.get('cmch', '无')}")
            print(f"  AIS识别号(aissbh): {record.get('aissbh', '无')}")
            print(f"  通过时间(tgsj): {record.get('tgsj', '无')}")
            print(f"  数据来源: {record.get('source_file', '未知')}")
            break
    
    print("\n演示完成！要使用完整功能请运行主程序。")


if __name__ == "__main__":
    # 根据命令行参数确定运行模式
    if len(sys.argv) > 1:
        if sys.argv[1] == "--test":
            print("进入系统测试模式...")
            test_system()
        elif sys.argv[1] == "--demo":
            print("进入演示模式...")
            simple_demo()
        else:
            print("可用参数:")
            print("  --test   : 系统测试")
            print("  --demo   : 简单演示（不需要模型）")
            print("  无参数   : 正常对话模式")
    else:
        print("进入港航数据查询对话模式...")
        chat_with_model()
