from modelscope import AutoModelForCausalLM, AutoTokenizer
from transformers import TextStreamer
import torch
import json
import os
import random
import numpy as np
import time
from tqdm import tqdm
import copy
import sys

# 路径配置
model_path = "Qwen3-4B"  # 本地模型路径
data_files = {
    "儿科": "儿科_data.json",
    "内科": "内科_data.json",
    "外科": "外科_data.json",
    "妇产科": "妇产科_data.json",
    "男科": "男科_data.json",
    "肿瘤科": "肿瘤科_data.json"
}

# 全局设置
USE_THINKING_MODE = True
MAX_NEW_TOKENS = 32768

class MedicalConsultationSystem:
    def __init__(self, model_path):
        print("正在加载模型，请稍候...")
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype="auto",
            device_map="auto"
        )
        print("模型加载完成！")
        
        # 加载医疗数据
        self.departments = list(data_files.keys())
        self.medical_data = self._load_medical_data()
        
        # GRPO算法参数
        self.eps_clip = 0.2
        self.value_loss_coef = 0.5
        self.entropy_coef = 0.01
        
    def _load_medical_data(self):
        """加载所有科室的医疗数据"""
        all_data = {}
        for dept, file_name in data_files.items():
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    dept_data = json.load(f)
                all_data[dept] = dept_data
                print(f"已加载{dept}数据: {len(dept_data)}条记录")
            except Exception as e:
                print(f"加载{dept}数据失败: {str(e)}")
                all_data[dept] = []
        return all_data

    def _create_system_prompt(self, department=None):
        """创建系统提示，根据科室定制"""
        base_prompt = "你是一位经验丰富的医学专家，专门进行医疗咨询。请按照以下医疗咨询流程进行对话：\n\n"
        base_prompt += "1. 主动引导患者详细描述症状和病史\n"
        base_prompt += "2. 有针对性地询问重要的临床信息\n"
        base_prompt += "3. 进行差异性诊断，考虑多种可能的诊断\n"
        base_prompt += "4. 采用链式推理策略：总结症状→提出诊断假设→确定下一步问诊目标\n"
        base_prompt += "5. 如果信息不足，继续追问以完善诊断\n"
        base_prompt += "6. 给出合理的诊断意见和建议\n\n"
        
        if department:
            base_prompt += f"你现在是{department}专家，请特别关注该领域的常见疾病和专业诊断。\n\n"
        
        base_prompt += "请确保回答专业、准确，同时通俗易懂。避免使用过于专业的术语，注重解释病情和治疗方案。"
        return base_prompt

    def _generate_response(self, messages, enable_thinking=True):
        """生成模型回复"""
        # 准备模型输入
        text = self.tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True,
            enable_thinking=enable_thinking
        )
        model_inputs = self.tokenizer([text], return_tensors="pt").to(self.model.device)

        # 设置生成参数，根据是否为思考模式选择不同的参数
        gen_kwargs = {
            "max_new_tokens": MAX_NEW_TOKENS,
            "do_sample": True,
        }
        
        if enable_thinking:
            # 思考模式参数
            gen_kwargs.update({
                "temperature": 0.6,
                "top_p": 0.95,
                "top_k": 20,
                "min_p": 0
            })
        else:
            # 非思考模式参数
            gen_kwargs.update({
                "temperature": 0.7,
                "top_p": 0.8,
                "top_k": 20,
                "min_p": 0
            })
        
        # 生成回复
        output_ids = self.model.generate(**model_inputs, **gen_kwargs)
        
        # 处理生成的文本
        full_output_ids = output_ids[0][len(model_inputs.input_ids[0]):].tolist()
        try:
            # 尝试找到</think>标记位置
            index = len(full_output_ids) - full_output_ids[::-1].index(151668)
        except ValueError:
            # 如果没有找到，则使用完整输出
            index = 0
        
        content = self.tokenizer.decode(full_output_ids[index:], skip_special_tokens=True).strip("\n")
        return content

    def _evaluate_response_quality(self, user_input, model_response, department=None):
        """
        使用批评者模型评估医生回复的质量
        返回0-1之间的评分，用于强化学习
        """
        # 构建批评者评估提示
        critic_messages = [
            {"role": "system", "content": "你是一个医疗对话质量评估专家。请评估医生回复的质量，关注以下几点：\n"
                                        "1. 是否积极引导患者详细描述症状\n"
                                        "2. 是否针对性提问获取关键信息\n"
                                        "3. 是否进行了差异性诊断\n"
                                        "4. 是否展示了清晰的推理过程\n"
                                        "5. 建议是否专业且易懂\n\n"
                                        "请给出0到1之间的评分，1表示完美回复。"}
        ]
        
        if department:
            critic_messages[0]["content"] += f"\n评估的是{department}科室的对话。"
        
        # 添加用户输入和模型回复
        critic_messages.append({"role": "user", "content": f"患者问题: {user_input}\n\n医生回复: {model_response}\n\n请给出评分并简要解释原因。"})
        
        # 获取批评者的评估
        critic_response = self._generate_response(critic_messages, enable_thinking=True)
        
        # 解析评分，如果无法解析则返回默认值0.5
        try:
            # 尝试从回复中提取0-1之间的评分
            score_text = critic_response.split("评分：")[-1].split("\n")[0] if "评分：" in critic_response else ""
            if not score_text:
                score_text = critic_response.split("分数：")[-1].split("\n")[0] if "分数：" in critic_response else ""
            
            # 清理文本并转换为浮点数
            score_text = ''.join(c for c in score_text if c.isdigit() or c == '.')
            score = float(score_text) if score_text else 0.5
            
            # 确保分数在0-1范围内
            score = min(max(score, 0), 1)
            return score
        except:
            return 0.5

    def train_grpo(self, num_episodes=10, epochs_per_episode=5, batch_size=4):
        """使用GRPO算法训练模型"""
        print("\n开始GRPO训练...")
        
        # GRPO 超参数设置
        lr = 1e-5
        epsilon = self.eps_clip  # PPO裁剪参数
        value_loss_coef = self.value_loss_coef
        entropy_coef = self.entropy_coef
        max_grad_norm = 0.5
        
        # 创建模型优化器
        optimizer = torch.optim.Adam(self.model.parameters(), lr=lr)
        
        # 创建价值网络（价值评估器）
        class ValueNetwork(torch.nn.Module):
            def __init__(self, input_dim=768, hidden_dim=256, output_dim=1):
                super(ValueNetwork, self).__init__()
                self.layers = torch.nn.Sequential(
                    torch.nn.Linear(input_dim, hidden_dim),
                    torch.nn.ReLU(),
                    torch.nn.Linear(hidden_dim, hidden_dim),
                    torch.nn.ReLU(),
                    torch.nn.Linear(hidden_dim, output_dim)
                )
                
            def forward(self, x):
                return self.layers(x)
        
        # 初始化价值网络
        value_net = ValueNetwork().to(self.model.device)
        value_optimizer = torch.optim.Adam(value_net.parameters(), lr=lr)
        
        for episode in range(num_episodes):
            print(f"\n第 {episode+1}/{num_episodes} 轮训练")
            
            # 从各科室数据中随机选择训练样本
            training_samples = []
            for dept, data in self.medical_data.items():
                if data:  # 如果该科室有数据
                    samples = random.sample(data, min(batch_size, len(data)))
                    for sample in samples:
                        # 提取用户问题和专家回复
                        if isinstance(sample, dict) and 'user_query' in sample and 'expert_response' in sample:
                            training_samples.append({
                                'department': dept,
                                'user_query': sample['user_query'],
                                'expert_response': sample['expert_response']
                            })
            
            if not training_samples:
                print("没有可用的训练样本，跳过此轮训练")
                continue
            
            # 针对每个样本进行训练
            for sample_idx, sample in enumerate(training_samples):
                dept = sample['department']
                user_query = sample['user_query']
                expert_response = sample['expert_response']
                
                print(f"\n训练样本 {sample_idx+1}/{len(training_samples)}: {dept}")
                
                # 创建系统提示和用户查询
                system_prompt = self._create_system_prompt(department=dept)
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_query}
                ]
                
                # GRPO 内循环训练
                for epoch in range(epochs_per_episode):
                    print(f"  GRPO Epoch {epoch+1}/{epochs_per_episode}")
                    
                    # 第1步：生成当前策略下的回复（旧策略）
                    old_response = self._generate_response(messages, enable_thinking=USE_THINKING_MODE)
                    old_score = self._evaluate_response_quality(user_query, old_response, dept)
                    
                    # 获取模型的隐藏状态作为特征输入
                    with torch.no_grad():
                        # 准备模型输入
                        text = self.tokenizer.apply_chat_template(
                            messages,
                            tokenize=False,
                            add_generation_prompt=True,
                            enable_thinking=USE_THINKING_MODE
                        )
                        model_inputs = self.tokenizer([text], return_tensors="pt").to(self.model.device)
                        
                        # 获取模型最后一层隐藏状态
                        outputs = self.model(**model_inputs, output_hidden_states=True)
                        hidden_states = outputs.hidden_states[-1]  # 获取最后一层隐藏状态
                        
                        # 使用平均池化获取句子表示
                        features = torch.mean(hidden_states, dim=1)  # [batch_size, hidden_size]
                    
                    # 第2步：计算旧策略下的动作价值
                    old_value = value_net(features).item()
                    
                    # 第3步：保存旧策略参数
                    old_policy_params = copy.deepcopy(self.model.state_dict())
                    
                    # 第4步：使用参考回复计算优势函数
                    # 评估参考回复质量
                    expert_score = self._evaluate_response_quality(user_query, expert_response, dept)
                    
                    # 计算优势（advantage）
                    advantage = expert_score - old_score
                    
                    # 第5步：使用GRPO更新策略网络
                    # 在这里，我们使用几个小批次的梯度更新来近似GRPO更新
                    for _ in range(5):  # 5个小批次更新
                        optimizer.zero_grad()
                        
                        # 生成新策略下的输出
                        new_outputs = self.model(**model_inputs, output_hidden_states=True)
                        new_logits = new_outputs.logits
                        new_hidden_states = new_outputs.hidden_states[-1]
                        
                        # 计算新旧策略的KL散度
                        with torch.no_grad():
                            old_outputs = self.model(**model_inputs)
                            old_logits = old_outputs.logits
                        
                        # 计算策略比率 (pi_new / pi_old)
                        log_probs_new = torch.nn.functional.log_softmax(new_logits, dim=-1)
                        log_probs_old = torch.nn.functional.log_softmax(old_logits, dim=-1)
                        ratio = torch.exp(log_probs_new.mean() - log_probs_old.mean())
                        
                        # 计算裁剪后的目标
                        surr1 = ratio * advantage
                        surr2 = torch.clamp(ratio, 1.0 - epsilon, 1.0 + epsilon) * advantage
                        policy_loss = -torch.min(surr1, surr2).mean()
                        
                        # 计算熵损失以促进探索
                        entropy = -(torch.nn.functional.softmax(new_logits, dim=-1) * log_probs_new).sum(dim=-1).mean()
                        entropy_loss = -entropy_coef * entropy
                        
                        # 总损失
                        loss = policy_loss + entropy_loss
                        
                        # 反向传播和优化
                        loss.backward()
                        torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_grad_norm)
                        optimizer.step()
                    
                    # 第6步：更新价值网络
                    # 从新隐藏状态中提取特征
                    with torch.no_grad():
                        new_features = torch.mean(new_hidden_states, dim=1)
                    
                    # 计算价值损失
                    for _ in range(3):  # 3个小批次更新价值网络
                        value_optimizer.zero_grad()
                        value_pred = value_net(new_features)
                        value_target = torch.tensor([[expert_score]], device=self.model.device)
                        value_loss = torch.nn.functional.mse_loss(value_pred, value_target)
                        value_loss.backward()
                        torch.nn.utils.clip_grad_norm_(value_net.parameters(), max_grad_norm)
                        value_optimizer.step()
                    
                    # 第7步：评估更新后的策略
                    new_response = self._generate_response(messages, enable_thinking=USE_THINKING_MODE)
                    new_score = self._evaluate_response_quality(user_query, new_response, dept)
                    
                    # 第8步：判断是否需要回滚
                    if new_score < old_score * 0.95:  # 如果性能下降超过5%
                        print(f"    性能下降，回滚更新：{new_score:.4f} < {old_score:.4f}")
                        self.model.load_state_dict(old_policy_params)  # 回滚到旧参数
                    else:
                        improvement = ((new_score - old_score) / max(old_score, 1e-6)) * 100
                        print(f"    更新后评分：{new_score:.4f} (改进: {improvement:.2f}%)")
            
            print(f"完成第 {episode+1} 轮GRPO训练")
            
            # 保存检查点
            try:
                checkpoint_path = f"medical_model_grpo_ep{episode+1}.pt"
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'value_net_state_dict': value_net.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'value_optimizer_state_dict': value_optimizer.state_dict(),
                    'episode': episode
                }, checkpoint_path)
                print(f"模型检查点已保存: {checkpoint_path}")
            except Exception as e:
                print(f"保存检查点失败: {str(e)}")
        
        print("GRPO训练完成！")

    def self_play_training(self, num_episodes=5):
        """实现自博弈训练"""
        print("\n开始自博弈训练...")
        
        for episode in range(num_episodes):
            print(f"\n第 {episode+1}/{num_episodes} 轮自博弈")
            
            # 随机选择一个科室进行自博弈
            department = random.choice(self.departments)
            print(f"选择科室: {department}")
            
            # 创建病例
            case = self._generate_patient_case(department)
            print(f"已生成病例: {case['condition']}")
            
            # 创建代理角色
            patient_agent_messages = [
                {"role": "system", "content": f"你是一位患者，你有以下症状和病史:\n{case['symptoms']}\n\n请根据症状自然地回答医生的问题，不要主动透露所有症状，而是根据医生的提问逐步提供信息。"}
            ]
            
            doctor_agent_messages = [
                {"role": "system", "content": self._create_system_prompt(department)}
            ]
            
            host_messages = [
                {"role": "system", "content": "你是医疗对话的主持人，负责评估对话质量并决定何时结束对话。评估标准包括：\n"
                                           "1. 医生是否充分了解患者症状\n"
                                           "2. 医生是否提供了专业且有用的诊断建议\n"
                                           "3. 患者问题是否得到了满意解答\n\n"
                                           "当对话达到合理结论时，你可以结束对话。"}
            ]
            
            critic_messages = [
                {"role": "system", "content": f"你是医疗对话的评估者，了解真实诊断情况：{case['condition']}。\n"
                                           f"请评估医生的诊断过程和最终诊断，特别关注:\n"
                                           f"1. 诊断是否准确或接近真实情况\n"
                                           f"2. 推理过程是否合理\n"
                                           f"3. 是否询问了关键的诊断信息\n"
                                           f"给出详细反馈，用于改进医生的诊断能力。"}
            ]
            
            # 开始模拟对话
            conversation_history = []
            turns = 0
            max_turns = 10
            is_dialogue_finished = False
            
            while not is_dialogue_finished and turns < max_turns:
                turns += 1
                print(f"\n对话轮次 {turns}/{max_turns}")
                
                if turns == 1:
                    # 第一轮，患者描述初始症状
                    patient_input = f"医生，我{case['initial_complaint']}"
                else:
                    # 医生提问后，患者回答
                    patient_agent_messages.append({"role": "user", "content": f"医生问题: {doctor_response}"})
                    patient_response = self._generate_response(patient_agent_messages, enable_thinking=False)
                    patient_agent_messages.append({"role": "assistant", "content": patient_response})
                    patient_input = patient_response
                
                print(f"患者: {patient_input}")
                conversation_history.append({"role": "user", "content": patient_input})
                
                # 医生回复
                doctor_agent_messages.append({"role": "user", "content": f"患者描述: {patient_input}"})
                doctor_response = self._generate_response(doctor_agent_messages, enable_thinking=USE_THINKING_MODE)
                doctor_agent_messages.append({"role": "assistant", "content": doctor_response})
                
                print(f"医生: {doctor_response[:100]}... (已省略)")
                conversation_history.append({"role": "assistant", "content": doctor_response})
                
                # 主持人评估是否结束对话
                host_input = f"当前对话进展:\n"
                for i, msg in enumerate(conversation_history):
                    prefix = "患者" if msg["role"] == "user" else "医生"
                    host_input += f"{prefix}: {msg['content'][:100]}...\n"
                
                host_input += "\n对话是否已经达到合理结论可以结束？请回答'是'或'否'并简要解释理由。"
                host_messages.append({"role": "user", "content": host_input})
                host_response = self._generate_response(host_messages, enable_thinking=False)
                host_messages.append({"role": "assistant", "content": host_response})
                
                is_dialogue_finished = "是" in host_response[:10]
                if is_dialogue_finished:
                    print("主持人决定结束对话")
                
            # 对话结束，批评者评估
            critic_input = f"完整对话记录:\n"
            for i, msg in enumerate(conversation_history):
                prefix = "患者" if msg["role"] == "user" else "医生"
                critic_input += f"{prefix}: {msg['content']}\n\n"
            
            critic_messages.append({"role": "user", "content": critic_input})
            critic_feedback = self._generate_response(critic_messages, enable_thinking=True)
            
            print("\n批评者反馈:")
            print(critic_feedback)
            
            # 将这次对话添加到训练数据中
            self._save_dialogue_for_training(department, conversation_history, critic_feedback)
            
        print("自博弈训练完成！")

    def _generate_patient_case(self, department):
        """生成模拟病例"""
        # 构建提示来生成病例
        case_gen_messages = [
            {"role": "system", "content": f"你是一位医学专家，请为{department}创建一个真实的患者病例，包括:\n"
                                       f"1. 主要疾病或状况\n"
                                       f"2. 详细症状描述\n"
                                       f"3. 患者基本信息(年龄、性别等)\n"
                                       f"4. 症状持续时间\n"
                                       f"5. 病史\n"
                                       f"以JSON格式返回，包含condition(病况)、symptoms(详细症状)、initial_complaint(初始主诉)三个字段。"}
        ]
        case_gen_messages.append({"role": "user", "content": f"请为{department}生成一个真实、详细的病例"})
        
        # 生成病例
        case_json_str = self._generate_response(case_gen_messages, enable_thinking=True)
        
        # 尝试解析JSON
        try:
            # 查找JSON开始和结束的位置
            start = case_json_str.find('{')
            end = case_json_str.rfind('}') + 1
            if start >= 0 and end > start:
                case_json_str = case_json_str[start:end]
                case = json.loads(case_json_str)
            else:
                raise ValueError("无法找到有效的JSON")
                
            # 确保包含所需字段
            required_fields = ["condition", "symptoms", "initial_complaint"]
            for field in required_fields:
                if field not in case:
                    raise ValueError(f"生成的病例缺少{field}字段")
                    
            return case
        except Exception as e:
            print(f"解析病例JSON时出错: {str(e)}")
            # 如果解析失败，返回默认病例
            return {
                "condition": f"{department}常见疾病",
                "symptoms": f"{department}相关症状描述",
                "initial_complaint": "最近感到不舒服"
            }
    
    def _save_dialogue_for_training(self, department, conversation, feedback):
        """保存对话用于未来训练"""
        # 这里可以实现保存对话到文件或数据库的逻辑
        # 简化实现：打印信息
        print(f"已保存{department}的对话用于训练，共{len(conversation)//2}轮对话")
        
    def chat_with_user(self):
        """与用户进行医疗咨询对话"""
        messages = []
        print("\n欢迎使用医疗咨询系统！")
        
        # 选择科室
        print("\n请选择科室：")
        for i, dept in enumerate(self.departments, 1):
            print(f"{i}. {dept}")
        print(f"{len(self.departments) + 1}. 综合咨询")
        
        while True:
            try:
                choice = int(input("\n请输入数字选择科室: "))
                if 1 <= choice <= len(self.departments) + 1:
                    break
                print("无效选择，请重新输入")
            except:
                print("请输入有效数字")
        
        # 设置科室相关的系统提示
        department = None if choice == len(self.departments) + 1 else self.departments[choice - 1]
        system_prompt = self._create_system_prompt(department)
        messages.append({"role": "system", "content": system_prompt})
        
        # 选择思考模式
        print("\n选择模式：")
        print("1. 开启思考模式（详细推理过程，更适合复杂问题）")
        print("2. 关闭思考模式（直接回答，更简洁）")
        
        while True:
            mode = input("\n请选择模式 (1/2): ")
            if mode in ['1', '2']:
                enable_thinking = (mode == '1')
                print(f"\n当前模式：{'开启' if enable_thinking else '关闭'}思考模式")
                break
            print("无效输入，请输入 1 或 2")
        
        # 主对话循环
        print("\n请描述您的症状或健康问题（输入'quit'退出，'switch'切换模式）：")
        while True:
            user_input = input("\n你: ")
            if user_input.lower() == 'quit':
                print("\n感谢您使用医疗咨询系统，祝您健康！")
                break
            elif user_input.lower() == 'switch':
                enable_thinking = not enable_thinking
                print(f"\n已切换到：{'开启' if enable_thinking else '关闭'}思考模式")
                continue
                
            messages.append({"role": "user", "content": user_input})
            
            print("\n正在分析您的问题...")
            
            # 使用流式输出
            response = self._generate_response(messages, enable_thinking)
            print(f"\n医生: {response}")
            
            # 将AI的回复添加到对话历史
            messages.append({"role": "assistant", "content": response})


def initialize_system():
    """初始化医疗咨询系统"""
    system = MedicalConsultationSystem(model_path)
    return system

def train_model():
    """训练模型函数"""
    system = initialize_system()
    
    # 执行自博弈训练
    system.self_play_training(num_episodes=2)
    
    # 执行GRPO训练
    system.train_grpo(num_episodes=3, epochs_per_episode=2)
    
    return system

def chat_with_model():
    """启动医疗咨询对话界面"""
    system = initialize_system()
    system.chat_with_user()

if __name__ == "__main__":
    # 根据命令行参数确定运行模式
    if len(sys.argv) > 1 and sys.argv[1] == "--train":
        print("进入训练模式...")
        system = train_model()
        # 训练后直接进入对话模式
        system.chat_with_user()
    else:
        chat_with_model()