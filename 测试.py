from modelscope import AutoModelForCausalLM, AutoTokenizer
import torch
import json
import random
import copy
import sys

# 路径配置
model_path = "Qwen3-4B"  # 本地模型路径
data_files = {
    "船舶数据1": "57d55cae476818831e41b8a7edbe3007.json",
    "船舶数据2": "dbe6a7821cce479ab6152a446c88b3c4.json"
}

# 全局设置
USE_THINKING_MODE = True
MAX_NEW_TOKENS = 32768

class ShippingRAGSystem:
    def __init__(self, model_path):
        print("正在加载模型，请稍候...")
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype="auto",
            device_map="auto"
        )
        print("模型加载完成！")

        # 加载船舶数据
        self.data_sources = list(data_files.keys())
        self.shipping_data = self._load_shipping_data()

        # RAG提示词模板
        self.rag_template = """作为港航数据专家，请根据以下知识库内容回答问题：
        上下文：data:{context}
        每一条{context}中包含一条船舶的JSON数据
        从{context}中解析JSON数据并提取以下结构化信息：
            观测点卡口的ID号:gcdid
            船舶的名称:cmch
            船舶的AIS识别号:aissbh
            船舶通过观测点卡口的时间:tgsj
        问题：{question}
        回答要求：
        1. 必须基于上下文内容
        2. 数据需标注来源文件
        3. 技术解释保持专业
        4. 超出知识范围明确说明
        """

        # GRPO算法参数
        self.eps_clip = 0.2
        self.value_loss_coef = 0.5
        self.entropy_coef = 0.01
        
    def _load_shipping_data(self):
        """加载所有船舶数据文件"""
        all_data = {}
        for source_name, file_name in data_files.items():
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    file_data = json.load(f)
                # 提取data字段中的船舶记录
                if isinstance(file_data, dict) and 'data' in file_data:
                    ship_records = file_data['data']
                else:
                    ship_records = file_data
                all_data[source_name] = ship_records
                print(f"已加载{source_name}数据: {len(ship_records)}条船舶记录")
            except Exception as e:
                print(f"加载{source_name}数据失败: {str(e)}")
                all_data[source_name] = []
        return all_data

    def _create_system_prompt(self, data_source=None):
        """创建系统提示，根据数据源定制"""
        base_prompt = "你是一位经验丰富的港航数据专家，专门分析船舶通行数据。请按照以下分析流程进行：\n\n"
        base_prompt += "1. 仔细解析提供的船舶JSON数据\n"
        base_prompt += "2. 提取关键信息：观测点卡口ID(gcdid)、船舶名称(cmch)、AIS识别号(aissbh)、通过时间(tgsj)\n"
        base_prompt += "3. 根据用户问题进行针对性分析\n"
        base_prompt += "4. 采用结构化回答：数据解析→信息提取→问题分析→结论总结\n"
        base_prompt += "5. 如果数据不完整或缺失，明确指出\n"
        base_prompt += "6. 给出专业的港航数据分析结论\n\n"

        if data_source:
            base_prompt += f"当前分析的是{data_source}，请特别关注该数据源的特点和数据质量。\n\n"

        base_prompt += "请确保回答专业、准确，基于实际数据内容，避免推测或编造信息。"
        return base_prompt

    def _search_relevant_ships(self, query, max_results=5):
        """根据查询搜索相关的船舶数据"""
        relevant_ships = []
        query_lower = query.lower()

        for source_name, ships in self.shipping_data.items():
            for ship in ships:
                # 检查船舶名称匹配
                if ship.get('cmch') and query_lower in ship['cmch'].lower():
                    relevant_ships.append((ship, source_name))
                # 检查AIS识别号匹配
                elif ship.get('aissbh') and str(ship['aissbh']) in query:
                    relevant_ships.append((ship, source_name))
                # 检查观测点ID匹配
                elif ship.get('gcdid') and ship['gcdid'] in query:
                    relevant_ships.append((ship, source_name))
                # 检查时间匹配
                elif ship.get('tgsj') and any(time_part in ship['tgsj'] for time_part in query.split()):
                    relevant_ships.append((ship, source_name))

        # 如果没有找到特定匹配，返回一些示例数据
        if not relevant_ships:
            for source_name, ships in self.shipping_data.items():
                for ship in ships[:max_results]:
                    relevant_ships.append((ship, source_name))
                if len(relevant_ships) >= max_results:
                    break

        return relevant_ships[:max_results]

    def _format_ship_context(self, ships_with_source):
        """格式化船舶数据作为上下文"""
        context_parts = []
        for ship, source_name in ships_with_source:
            ship_json = json.dumps(ship, ensure_ascii=False, indent=2)
            context_parts.append(f"来源文件: {source_name}\n{ship_json}")
        return "\n\n".join(context_parts)

    def answer_with_rag(self, question):
        """使用RAG模式回答问题"""
        # 搜索相关船舶数据
        relevant_ships = self._search_relevant_ships(question)

        # 格式化上下文
        context = self._format_ship_context(relevant_ships)

        # 使用RAG模板构建提示
        rag_prompt = self.rag_template.format(context=context, question=question)

        # 生成回答
        messages = [
            {"role": "system", "content": "你是港航数据专家，请根据提供的数据回答问题。"},
            {"role": "user", "content": rag_prompt}
        ]

        response = self._generate_response(messages, enable_thinking=True)
        return response, context

    def _generate_response(self, messages, enable_thinking=True):
        """生成模型回复"""
        # 准备模型输入
        text = self.tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True,
            enable_thinking=enable_thinking
        )
        model_inputs = self.tokenizer([text], return_tensors="pt").to(self.model.device)

        # 设置生成参数，根据是否为思考模式选择不同的参数
        gen_kwargs = {
            "max_new_tokens": MAX_NEW_TOKENS,
            "do_sample": True,
        }
        
        if enable_thinking:
            # 思考模式参数
            gen_kwargs.update({
                "temperature": 0.6,
                "top_p": 0.95,
                "top_k": 20,
                "min_p": 0
            })
        else:
            # 非思考模式参数
            gen_kwargs.update({
                "temperature": 0.7,
                "top_p": 0.8,
                "top_k": 20,
                "min_p": 0
            })
        
        # 生成回复
        output_ids = self.model.generate(**model_inputs, **gen_kwargs)
        
        # 处理生成的文本
        full_output_ids = output_ids[0][len(model_inputs.input_ids[0]):].tolist()
        try:
            # 尝试找到</think>标记位置
            index = len(full_output_ids) - full_output_ids[::-1].index(151668)
        except ValueError:
            # 如果没有找到，则使用完整输出
            index = 0
        
        content = self.tokenizer.decode(full_output_ids[index:], skip_special_tokens=True).strip("\n")
        return content

    def _evaluate_response_quality(self, user_input, model_response, data_source=None):
        """
        使用批评者模型评估港航数据分析回复的质量
        返回0-1之间的评分，用于强化学习
        """
        # 构建批评者评估提示
        critic_messages = [
            {"role": "system", "content": "你是一个港航数据分析质量评估专家。请评估数据分析回复的质量，关注以下几点：\n"
                                        "1. 是否正确解析了船舶JSON数据\n"
                                        "2. 是否准确提取了关键信息(gcdid、cmch、aissbh、tgsj)\n"
                                        "3. 是否基于实际数据内容进行分析\n"
                                        "4. 是否展示了清晰的分析过程\n"
                                        "5. 回答是否专业且准确\n\n"
                                        "请给出0到1之间的评分，1表示完美回复。"}
        ]

        if data_source:
            critic_messages[0]["content"] += f"\n评估的是{data_source}的数据分析。"
        
        # 添加用户输入和模型回复
        critic_messages.append({"role": "user", "content": f"患者问题: {user_input}\n\n医生回复: {model_response}\n\n请给出评分并简要解释原因。"})
        
        # 获取批评者的评估
        critic_response = self._generate_response(critic_messages, enable_thinking=True)
        
        # 解析评分，如果无法解析则返回默认值0.5
        try:
            # 尝试从回复中提取0-1之间的评分
            score_text = critic_response.split("评分：")[-1].split("\n")[0] if "评分：" in critic_response else ""
            if not score_text:
                score_text = critic_response.split("分数：")[-1].split("\n")[0] if "分数：" in critic_response else ""
            
            # 清理文本并转换为浮点数
            score_text = ''.join(c for c in score_text if c.isdigit() or c == '.')
            score = float(score_text) if score_text else 0.5
            
            # 确保分数在0-1范围内
            score = min(max(score, 0), 1)
            return score
        except:
            return 0.5

    def train_grpo(self, num_episodes=10, epochs_per_episode=5, batch_size=4):
        """使用GRPO算法训练模型"""
        print("\n开始GRPO训练...")
        
        # GRPO 超参数设置
        lr = 1e-5
        epsilon = self.eps_clip  # PPO裁剪参数
        # value_loss_coef = self.value_loss_coef  # 暂未使用
        entropy_coef = self.entropy_coef
        max_grad_norm = 0.5
        
        # 创建模型优化器
        optimizer = torch.optim.Adam(self.model.parameters(), lr=lr)
        
        # 创建价值网络（价值评估器）
        class ValueNetwork(torch.nn.Module):
            def __init__(self, input_dim=768, hidden_dim=256, output_dim=1):
                super(ValueNetwork, self).__init__()
                self.layers = torch.nn.Sequential(
                    torch.nn.Linear(input_dim, hidden_dim),
                    torch.nn.ReLU(),
                    torch.nn.Linear(hidden_dim, hidden_dim),
                    torch.nn.ReLU(),
                    torch.nn.Linear(hidden_dim, output_dim)
                )
                
            def forward(self, x):
                return self.layers(x)
        
        # 初始化价值网络
        value_net = ValueNetwork().to(self.model.device)
        value_optimizer = torch.optim.Adam(value_net.parameters(), lr=lr)
        
        for episode in range(num_episodes):
            print(f"\n第 {episode+1}/{num_episodes} 轮训练")
            
            # 从各数据源中随机选择训练样本
            training_samples = []
            for source_name, data in self.shipping_data.items():
                if data:  # 如果该数据源有数据
                    samples = random.sample(data, min(batch_size, len(data)))
                    for sample in samples:
                        # 为船舶数据生成问答对
                        if isinstance(sample, dict):
                            # 生成基于船舶数据的问题和答案
                            ship_info = json.dumps(sample, ensure_ascii=False)
                            user_query = f"请分析这条船舶数据：{ship_info}"
                            expert_response = f"根据数据分析，这是一条关于船舶的记录，包含观测点ID、船舶名称、AIS号等信息。"
                            training_samples.append({
                                'data_source': source_name,
                                'user_query': user_query,
                                'expert_response': expert_response
                            })
            
            if not training_samples:
                print("没有可用的训练样本，跳过此轮训练")
                continue
            
            # 针对每个样本进行训练
            for sample_idx, sample in enumerate(training_samples):
                source_name = sample['data_source']
                user_query = sample['user_query']
                expert_response = sample['expert_response']

                print(f"\n训练样本 {sample_idx+1}/{len(training_samples)}: {source_name}")

                # 创建系统提示和用户查询
                system_prompt = self._create_system_prompt(data_source=source_name)
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_query}
                ]
                
                # GRPO 内循环训练
                for epoch in range(epochs_per_episode):
                    print(f"  GRPO Epoch {epoch+1}/{epochs_per_episode}")
                    
                    # 第1步：生成当前策略下的回复（旧策略）
                    old_response = self._generate_response(messages, enable_thinking=USE_THINKING_MODE)
                    old_score = self._evaluate_response_quality(user_query, old_response, source_name)
                    
                    # 获取模型的隐藏状态作为特征输入
                    with torch.no_grad():
                        # 准备模型输入
                        text = self.tokenizer.apply_chat_template(
                            messages,
                            tokenize=False,
                            add_generation_prompt=True,
                            enable_thinking=USE_THINKING_MODE
                        )
                        model_inputs = self.tokenizer([text], return_tensors="pt").to(self.model.device)
                        
                        # 获取模型最后一层隐藏状态
                        # outputs = self.model(**model_inputs, output_hidden_states=True)
                        # hidden_states = outputs.hidden_states[-1]  # 获取最后一层隐藏状态

                        # 使用平均池化获取句子表示
                        # features = torch.mean(hidden_states, dim=1)  # [batch_size, hidden_size] 暂未使用

                    # 第2步：计算旧策略下的动作价值
                    # old_value = value_net(features).item()  # 暂未使用
                    
                    # 第3步：保存旧策略参数
                    old_policy_params = copy.deepcopy(self.model.state_dict())
                    
                    # 第4步：使用参考回复计算优势函数
                    # 评估参考回复质量
                    expert_score = self._evaluate_response_quality(user_query, expert_response, source_name)
                    
                    # 计算优势（advantage）
                    advantage = expert_score - old_score
                    
                    # 第5步：使用GRPO更新策略网络
                    # 在这里，我们使用几个小批次的梯度更新来近似GRPO更新
                    for _ in range(5):  # 5个小批次更新
                        optimizer.zero_grad()
                        
                        # 生成新策略下的输出
                        new_outputs = self.model(**model_inputs, output_hidden_states=True)
                        new_logits = new_outputs.logits
                        new_hidden_states = new_outputs.hidden_states[-1]
                        
                        # 计算新旧策略的KL散度
                        with torch.no_grad():
                            old_outputs = self.model(**model_inputs)
                            old_logits = old_outputs.logits
                        
                        # 计算策略比率 (pi_new / pi_old)
                        log_probs_new = torch.nn.functional.log_softmax(new_logits, dim=-1)
                        log_probs_old = torch.nn.functional.log_softmax(old_logits, dim=-1)
                        ratio = torch.exp(log_probs_new.mean() - log_probs_old.mean())
                        
                        # 计算裁剪后的目标
                        surr1 = ratio * advantage
                        surr2 = torch.clamp(ratio, 1.0 - epsilon, 1.0 + epsilon) * advantage
                        policy_loss = -torch.min(surr1, surr2).mean()
                        
                        # 计算熵损失以促进探索
                        entropy = -(torch.nn.functional.softmax(new_logits, dim=-1) * log_probs_new).sum(dim=-1).mean()
                        entropy_loss = -entropy_coef * entropy
                        
                        # 总损失
                        loss = policy_loss + entropy_loss
                        
                        # 反向传播和优化
                        loss.backward()
                        torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_grad_norm)
                        optimizer.step()
                    
                    # 第6步：更新价值网络
                    # 从新隐藏状态中提取特征
                    with torch.no_grad():
                        new_features = torch.mean(new_hidden_states, dim=1)
                    
                    # 计算价值损失
                    for _ in range(3):  # 3个小批次更新价值网络
                        value_optimizer.zero_grad()
                        value_pred = value_net(new_features)
                        value_target = torch.tensor([[expert_score]], device=self.model.device)
                        value_loss = torch.nn.functional.mse_loss(value_pred, value_target)
                        value_loss.backward()
                        torch.nn.utils.clip_grad_norm_(value_net.parameters(), max_grad_norm)
                        value_optimizer.step()
                    
                    # 第7步：评估更新后的策略
                    new_response = self._generate_response(messages, enable_thinking=USE_THINKING_MODE)
                    new_score = self._evaluate_response_quality(user_query, new_response, source_name)
                    
                    # 第8步：判断是否需要回滚
                    if new_score < old_score * 0.95:  # 如果性能下降超过5%
                        print(f"    性能下降，回滚更新：{new_score:.4f} < {old_score:.4f}")
                        self.model.load_state_dict(old_policy_params)  # 回滚到旧参数
                    else:
                        improvement = ((new_score - old_score) / max(old_score, 1e-6)) * 100
                        print(f"    更新后评分：{new_score:.4f} (改进: {improvement:.2f}%)")
            
            print(f"完成第 {episode+1} 轮GRPO训练")
            
            # 保存检查点
            try:
                checkpoint_path = f"medical_model_grpo_ep{episode+1}.pt"
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'value_net_state_dict': value_net.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'value_optimizer_state_dict': value_optimizer.state_dict(),
                    'episode': episode
                }, checkpoint_path)
                print(f"模型检查点已保存: {checkpoint_path}")
            except Exception as e:
                print(f"保存检查点失败: {str(e)}")
        
        print("GRPO训练完成！")

    def generate_shipping_qa_pairs(self, num_pairs=10):
        """生成船舶数据问答对用于训练"""
        print("\n开始生成船舶数据问答对...")

        qa_pairs = []
        for i in range(num_pairs):
            # 随机选择数据源和船舶记录
            source_name = random.choice(self.data_sources)
            ships = self.shipping_data[source_name]
            if not ships:
                continue

            ship = random.choice(ships)

            # 生成不同类型的问题
            question_types = [
                f"请分析船舶{ship.get('cmch', '未知')}的通行记录",
                f"查找AIS识别号为{ship.get('aissbh', '未知')}的船舶信息",
                f"分析观测点{ship.get('gcdid', '未知')}的船舶通行情况",
                f"显示{ship.get('tgsj', '未知时间')}的船舶数据"
            ]

            question = random.choice(question_types)

            # 生成标准答案
            answer = f"""根据数据分析：
观测点卡口ID: {ship.get('gcdid', '数据缺失')}
船舶名称: {ship.get('cmch', '数据缺失')}
AIS识别号: {ship.get('aissbh', '数据缺失')}
通过时间: {ship.get('tgsj', '数据缺失')}
数据来源: {source_name}

这是一条完整的船舶通行记录，包含了船舶的基本识别信息和通过观测点的时间信息。"""

            qa_pairs.append({
                'question': question,
                'answer': answer,
                'source': source_name,
                'ship_data': ship
            })

            print(f"生成问答对 {i+1}/{num_pairs}: {question[:50]}...")

        print(f"共生成 {len(qa_pairs)} 个问答对")
        return qa_pairs

    def save_qa_pairs_for_training(self, qa_pairs, filename="shipping_qa_pairs.json"):
        """保存问答对用于训练"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(qa_pairs, f, ensure_ascii=False, indent=2)
            print(f"已保存 {len(qa_pairs)} 个问答对到 {filename}")
        except Exception as e:
            print(f"保存问答对失败: {str(e)}")
        
    def chat_with_user(self):
        """与用户进行港航数据分析对话"""
        messages = []
        print("\n欢迎使用港航数据分析系统！")

        # 选择数据源
        print("\n请选择数据源：")
        for i, source in enumerate(self.data_sources, 1):
            print(f"{i}. {source}")
        print(f"{len(self.data_sources) + 1}. 综合分析")
        print(f"{len(self.data_sources) + 2}. RAG模式（智能检索回答）")

        while True:
            try:
                choice = int(input("\n请输入数字选择模式: "))
                if 1 <= choice <= len(self.data_sources) + 2:
                    break
                print("无效选择，请重新输入")
            except:
                print("请输入有效数字")

        # 设置数据源相关的系统提示
        if choice == len(self.data_sources) + 2:
            # RAG模式
            use_rag = True
            data_source = None
            print("\n已选择RAG模式，系统将智能检索相关数据回答问题")
        else:
            use_rag = False
            data_source = None if choice == len(self.data_sources) + 1 else self.data_sources[choice - 1]
            system_prompt = self._create_system_prompt(data_source)
            messages.append({"role": "system", "content": system_prompt})
        
        # 选择思考模式
        print("\n选择模式：")
        print("1. 开启思考模式（详细推理过程，更适合复杂问题）")
        print("2. 关闭思考模式（直接回答，更简洁）")
        
        while True:
            mode = input("\n请选择模式 (1/2): ")
            if mode in ['1', '2']:
                enable_thinking = (mode == '1')
                print(f"\n当前模式：{'开启' if enable_thinking else '关闭'}思考模式")
                break
            print("无效输入，请输入 1 或 2")
        
        # 主对话循环
        print("\n请输入您的港航数据问题（输入'quit'退出，'switch'切换模式）：")
        while True:
            user_input = input("\n你: ")
            if user_input.lower() == 'quit':
                print("\n感谢您使用港航数据分析系统！")
                break
            elif user_input.lower() == 'switch':
                enable_thinking = not enable_thinking
                print(f"\n已切换到：{'开启' if enable_thinking else '关闭'}思考模式")
                continue

            print("\n正在分析您的问题...")

            if use_rag:
                # 使用RAG模式
                response, context = self.answer_with_rag(user_input)
                print(f"\n数据专家: {response}")
                print(f"\n[使用的数据上下文]:\n{context[:500]}..." if len(context) > 500 else f"\n[使用的数据上下文]:\n{context}")
            else:
                # 使用普通对话模式
                messages.append({"role": "user", "content": user_input})
                response = self._generate_response(messages, enable_thinking)
                print(f"\n数据专家: {response}")
                # 将AI的回复添加到对话历史
                messages.append({"role": "assistant", "content": response})


def initialize_system():
    """初始化港航数据分析系统"""
    system = ShippingRAGSystem(model_path)
    return system

def train_model():
    """训练模型函数"""
    system = initialize_system()

    # 生成船舶数据问答对
    qa_pairs = system.generate_shipping_qa_pairs(num_pairs=20)
    system.save_qa_pairs_for_training(qa_pairs)

    # 执行GRPO训练
    system.train_grpo(num_episodes=3, epochs_per_episode=2)

    return system

def chat_with_model():
    """启动港航数据分析对话界面"""
    system = initialize_system()
    system.chat_with_user()

def test_rag_system():
    """测试RAG系统功能"""
    system = initialize_system()

    # 测试问题
    test_questions = [
        "请分析船舶江淮货12的通行记录",
        "查找AIS识别号为3957319的船舶信息",
        "显示2024年9月9日的船舶通行数据",
        "分析观测点卡口1EC06D9D04FD6B209FBA93E91E075的通行情况"
    ]

    print("=== 港航数据RAG系统测试 ===\n")

    for i, question in enumerate(test_questions, 1):
        print(f"测试问题 {i}: {question}")
        print("-" * 50)

        try:
            response, context = system.answer_with_rag(question)
            print(f"回答: {response}")
            print(f"\n使用的数据上下文:\n{context[:300]}...")
            print("=" * 80 + "\n")
        except Exception as e:
            print(f"错误: {str(e)}")
            print("=" * 80 + "\n")

def simple_demo():
    """简单演示功能，不需要加载大模型"""
    print("=== 港航数据系统演示 ===\n")

    # 直接加载数据文件进行演示
    data_files_demo = {
        "船舶数据1": "57d55cae476818831e41b8a7edbe3007.json",
        "船舶数据2": "dbe6a7821cce479ab6152a446c88b3c4.json"
    }

    print("正在加载船舶数据...")
    all_data = {}
    for source_name, file_name in data_files_demo.items():
        try:
            with open(file_name, 'r', encoding='utf-8') as f:
                file_data = json.load(f)
            # 提取data字段中的船舶记录
            if isinstance(file_data, dict) and 'data' in file_data:
                ship_records = file_data['data']
            else:
                ship_records = file_data
            all_data[source_name] = ship_records
            print(f"已加载{source_name}: {len(ship_records)}条船舶记录")
        except Exception as e:
            print(f"加载{source_name}失败: {str(e)}")
            all_data[source_name] = []

    # 显示数据样例
    print("\n=== 数据样例 ===")
    for source_name, ships in all_data.items():
        if ships:
            print(f"\n{source_name}的第一条记录:")
            ship = ships[0]
            print(f"  观测点卡口ID: {ship.get('gcdid', '无')}")
            print(f"  船舶名称: {ship.get('cmch', '无')}")
            print(f"  AIS识别号: {ship.get('aissbh', '无')}")
            print(f"  通过时间: {ship.get('tgsj', '无')}")

    # RAG模板演示
    rag_template = """作为港航数据专家，请根据以下知识库内容回答问题：
    上下文：data:{context}
    每一条{context}中包含一条船舶的JSON数据
    从{context}中解析JSON数据并提取以下结构化信息：
        观测点卡口的ID号:gcdid
        船舶的名称:cmch
        船舶的AIS识别号:aissbh
        船舶通过观测点卡口的时间:tgsj
    问题：{question}
    回答要求：
    1. 必须基于上下文内容
    2. 数据需标注来源文件
    3. 技术解释保持专业
    4. 超出知识范围明确说明
    """

    print(f"\n=== RAG模板演示 ===")
    print("RAG提示词模板:")
    print(rag_template)

    print("\n演示完成！要使用完整功能请运行主程序。")

if __name__ == "__main__":
    # 根据命令行参数确定运行模式
    if len(sys.argv) > 1:
        if sys.argv[1] == "--train":
            print("进入训练模式...")
            system = train_model()
            # 训练后直接进入对话模式
            system.chat_with_user()
        elif sys.argv[1] == "--test":
            print("进入RAG系统测试模式...")
            test_rag_system()
        else:
            print("未知参数，进入普通对话模式...")
            chat_with_model()
    else:
        print("进入港航数据分析对话模式...")
        chat_with_model()